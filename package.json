{"name": "backend", "version": "1.0.0", "main": "index.js", "scripts": {"test": "vitest --run", "test:watch": "vitest", "test:ui": "vitest --ui", "start": "node dist/index.js", "dev": "nodemon src/index.ts", "dev-server": "ts-node dev-server.ts", "build": "tsc"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@types/multer": "^1.4.12", "@types/selenium-webdriver": "^4.1.28", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.8", "@types/uuid": "^10.0.0", "bcrypt": "^6.0.0", "chromedriver": "^136.0.3", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "geckodriver": "^5.0.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.14.2", "multer": "^1.4.5-lts.2", "qrcode": "^1.5.4", "selenium-webdriver": "^4.32.0", "socket.io": "^4.8.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "uuid": "^11.1.0", "whatsapp-web.js": "^1.27.0"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/cors": "^2.8.18", "@types/express": "^5.0.1", "@types/jsonwebtoken": "^9.0.9", "@types/mongoose": "^5.11.96", "@types/qrcode": "^1.5.5", "@types/socket.io": "^3.0.1", "@vitest/ui": "^3.2.4", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3", "vitest": "^3.2.4"}}